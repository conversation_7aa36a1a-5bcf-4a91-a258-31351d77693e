<script setup lang="ts">
import {
    ArrowLeftIcon,
    PlayIcon,
    BookmarkIcon,
    CodeBracketIcon,
    ShareIcon,
    EllipsisVerticalIcon,
} from "@heroicons/vue/24/outline";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

defineProps<{
    currentProjectID: string;
}>();

const emit = defineEmits(["return", "debug", "save", "publish", "share"]);
</script>

<template>
    <div
        class="absolute top-2 left-2 z-50 bg-[#fafafa] rounded-xl p-2 shadow-md bg-opacity-65 box-border flex flex-wrap gap-2 max-md:max-w-[320px]"
    >
        <button
            class="btn inline-flex items-center btn-sm"
            @click="emit('return')"
        >
            <ArrowLeftIcon class="w-4 h-4" />
            {{ t("return") }}
        </button>
        <button
            class="btn btn-secondary inline-flex items-center btn-sm"
            @click="emit('debug')"
        >
            <PlayIcon class="w-4 h-4" />
            {{ t("debug") }}
        </button>
        <button
            class="btn btn-primary inline-flex items-center btn-sm"
            @click="emit('save')"
        >
            <BookmarkIcon class="w-4 h-4" />
            {{ t("save") }}
        </button>
        <div class="dropdown dropdown-hover dropdown-end" v-if="currentProjectID">
            <label tabindex="0" class="btn inline-flex items-center btn-sm px-1">
                <EllipsisVerticalIcon class="w-4 h-4" />
            </label>
            <ul
                tabindex="0"
                class="dropdown-content menu p-2 gap-2 shadow bg-base-100 rounded-box w-52 -left-5"
            >
                <li>
                    <a @click="emit('publish')">
                        <CodeBracketIcon class="w-4 h-4" />
                        {{ t("publish_api") }}
                    </a>
                </li>
            </ul>
        </div>
    </div>
</template>
