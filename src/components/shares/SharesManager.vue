<script setup lang="ts">
	import { ref, computed, watch } from "vue";
	import { useI18n } from "vue-i18n";
	import { Error, Success } from "@/utils/notify";
	import { SharesAPI, type WorkflowShare, type ShareStats } from "@/api/shares";
	import {
		XMarkIcon,
		ShareIcon,
		EyeIcon,
		PlayIcon,
		DocumentDuplicateIcon,
	} from "@heroicons/vue/24/outline";
	import CreateShareModal from "./CreateShareModal.vue";
	import EditShareModal from "./EditShareModal.vue";
	import ShareStatsModal from "./ShareStats.vue";
	import ShareLogs from "./ShareLogs.vue";
	import ShareLinkCopy from "./ShareLinkCopy.vue";

	const { t } = useI18n();

	const props = defineProps<{
		modelValue: boolean;
		workflowId: string;
		workflowName: string;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: boolean): void;
	}>();

	const show = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const shares = ref<WorkflowShare[]>([]);
	const loading = ref(false);
	const showCreateModal = ref(false);
	const showEditModal = ref(false);
	const showStatsModal = ref(false);
	const showLogsModal = ref(false);
	const selectedShare = ref<WorkflowShare | null>(null);
	const selectedShareStats = ref<ShareStats | null>(null);

	const loadShares = async () => {
		loading.value = true;
		try {
			const res = await SharesAPI.getWorkflowShares(props.workflowId);
			shares.value = res.shares;
		} catch (err) {
			console.error(err);
			Error(t("error"), t("shares.failed_to_load_shares"));
		} finally {
			loading.value = false;
		}
	};

	const handleDeleteShare = async (share: WorkflowShare) => {
		if (!confirm(t("shares.confirm_delete_share"))) return;

		try {
			await SharesAPI.deleteShare(props.workflowId, share.share_uuid);
			Success(t("success"), t("shares.delete_share_success"));
			await loadShares();
		} catch (err) {
			Error(t("error"), t("shares.delete_share_error"));
		}
	};

	const handleToggleShare = async (share: WorkflowShare) => {
		try {
			await SharesAPI.updateShare(props.workflowId, share.share_uuid, {
				is_enabled: !share.is_enabled,
			});
			Success(t("success"), t("shares.toggle_share_success"));
			await loadShares();
		} catch (err) {
			Error(t("error"), t("shares.toggle_share_error"));
		}
	};

	const handleViewStats = async (share: WorkflowShare) => {
		try {
			const res = await SharesAPI.getShareDetails(props.workflowId, share.share_uuid);
			selectedShare.value = res.share;
			selectedShareStats.value = res.stats;
			showStatsModal.value = true;
		} catch (err) {
			Error(t("error"), t("shares.failed_to_load_stats"));
		}
	};

	const handleViewLogs = (share: WorkflowShare) => {
		selectedShare.value = share;
		showLogsModal.value = true;
	};

	const handleEditShare = (share: WorkflowShare) => {
		selectedShare.value = share;
		showEditModal.value = true;
	};

	const handleCreateSuccess = () => {
		showCreateModal.value = false;
		loadShares();
	};

	const handleEditSuccess = () => {
		showEditModal.value = false;
		loadShares();
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleString();
	};

	const isExpired = (share: WorkflowShare) => {
		if (!share.expires_at) return false;
		return new Date(share.expires_at) < new Date();
	};

	const getShareUrl = (shareUuid: string) => {
		return `${window.location.origin}/shared/${shareUuid}`;
	};

	watch(
		() => show.value,
		(newVal) => {
			if (newVal) {
				loadShares();
			}
		}
	);
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': show }">
			<div class="modal-box relative max-w-6xl">
				<div class="flex flex-col space-y-6">
					<div class="flex justify-between items-center">
						<h3 class="font-bold text-2xl flex items-center gap-2">
							<ShareIcon class="w-6 h-6" />
							{{ t("shares.workflow_shares") }}
						</h3>
						<button class="btn btn-ghost btn-circle btn-sm" @click="show = false">
							<XMarkIcon class="w-5 h-5" />
						</button>
					</div>

					<div class="flex flex-col space-y-4">
						<div class="flex justify-between items-center">
							<p class="text-base-content/70">
								{{ t("shares.manage_shares_for", { workflow: workflowName }) }}
							</p>
							<button class="btn btn-primary" @click="showCreateModal = true">
								<ShareIcon class="w-4 h-4 mr-2" />
								{{ t("shares.create_share") }}
							</button>
						</div>

						<div class="bg-base-200 rounded-lg p-4">
							<div v-if="loading" class="flex items-center justify-center py-8">
								<span class="loading loading-spinner loading-md"></span>
							</div>
							<div
								v-else-if="shares.length === 0"
								class="flex flex-col items-center justify-center py-8 text-base-content/70"
							>
								<ShareIcon class="w-12 h-12 mb-4" />
								<p class="text-lg font-medium">
									{{ t("shares.no_shares") }}
								</p>
								<p class="text-sm mt-2">
									{{ t("shares.create_first_share") }}
								</p>
							</div>
							<div v-else class="space-y-3">
								<div
									v-for="share in shares"
									:key="share.share_uuid"
									class="flex items-center justify-between p-4 bg-base-100 rounded-lg border border-base-200 hover:border-primary transition-colors"
								>
									<div class="flex-1">
										<div class="flex items-center space-x-3 mb-2">
											<h4 class="font-semibold text-lg">
												{{ share.title }}
											</h4>
											<span
												class="badge"
												:class="
													share.is_enabled && !isExpired(share)
														? 'badge-success'
														: 'badge-error'
												"
											>
												{{
													share.is_enabled
														? isExpired(share)
															? t("shares.expired")
															: t("shares.active")
														: t("shares.disabled")
												}}
											</span>
											<span class="badge badge-outline">
												{{
													share.bill_to === "sharer"
														? t("shares.sharer_pays")
														: t("shares.runner_pays")
												}}
											</span>
										</div>
										<p class="text-base-content/70 mb-2">
											{{
												share.description || t("shares.no_description")
											}}
										</p>
										<div
											class="flex items-center space-x-4 text-sm text-base-content/60"
										>
											<span
												>{{ t("shares.created_at") }}:
												{{ formatDate(share.created_at) }}</span
											>
											<span v-if="share.expires_at">
												{{ t("shares.expires_at") }}:
												{{ formatDate(share.expires_at) }}
											</span>
										</div>
										<div
											class="flex items-center space-x-4 text-sm text-base-content/60 mt-1"
										>
											<span class="flex items-center gap-1">
												<EyeIcon class="w-4 h-4" />
												{{ t("shares.view") }}:
												{{
													share.ui_config.allow_view
														? t("shares.allowed")
														: t("shares.disabled")
												}}
											</span>
											<span class="flex items-center gap-1">
												<PlayIcon class="w-4 h-4" />
												{{ t("shares.run") }}:
												{{
													share.ui_config.allow_run
														? t("shares.allowed")
														: t("shares.disabled")
												}}
											</span>
											<span class="flex items-center gap-1">
												<DocumentDuplicateIcon class="w-4 h-4" />
												{{ t("shares.copy") }}:
												{{
													share.ui_config.allow_copy
														? t("shares.allowed")
														: t("shares.disabled")
												}}
											</span>
										</div>
									</div>

									<div class="flex items-center space-x-2">
										<ShareLinkCopy :share-uuid="share.share_uuid" />
										<button
											class="btn btn-ghost btn-sm"
											@click="handleViewStats(share)"
										>
											{{ t("shares.stats") }}
										</button>
										<button
											class="btn btn-ghost btn-sm"
											@click="handleViewLogs(share)"
										>
											{{ t("shares.logs") }}
										</button>
										<button
											class="btn btn-ghost btn-sm"
											@click="handleEditShare(share)"
										>
											{{ t("shares.edit") }}
										</button>
										<button
											class="btn btn-ghost btn-sm"
											:class="
												share.is_enabled
													? 'btn-warning'
													: 'btn-success'
											"
											@click="handleToggleShare(share)"
										>
											{{
												share.is_enabled
													? t("shares.disable")
													: t("shares.enable")
											}}
										</button>
										<button
											class="btn btn-error btn-sm"
											@click="handleDeleteShare(share)"
										>
											{{ t("shares.delete") }}
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<label class="modal-backdrop" @click="show = false"></label>
		</div>
	</Teleport>

	<!-- Create Share Modal -->
	<CreateShareModal
		v-model="showCreateModal"
		:workflow-id="workflowId"
		:workflow-name="workflowName"
		@create-success="handleCreateSuccess"
	/>

	<!-- Edit Share Modal -->
	<EditShareModal
		v-model="showEditModal"
		:workflow-id="workflowId"
		:share="selectedShare"
		@edit-success="handleEditSuccess"
	/>

	<!-- Stats Modal -->
	<ShareStatsModal
		v-model="showStatsModal"
		:share="selectedShare"
		:stats="selectedShareStats"
	/>

	<!-- Logs Modal -->
	<ShareLogs v-model="showLogsModal" :workflow-id="workflowId" :share="selectedShare" />
</template>
