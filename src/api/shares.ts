import axios from "@/api/axios";

// Data Models
export interface ShareRateLimit {
	enabled: boolean;
	daily_run_limit: number;
	hourly_run_limit: number;
	per_ip_daily_limit: number;
	cooldown_seconds: number;
}

export interface ShareUIConfig {
	allow_view: boolean;
	allow_run: boolean;
	allow_copy: boolean;
	show_workflow_graph: boolean;
	welcome_message: string;
	custom_theme: Record<string, any>;
}

export interface WorkflowShare {
	id: number;
	share_uuid: string;
	workflow_id: number;
	owner_id: number;
	title: string;
	description: string;
	bill_to: "sharer" | "runner";
	is_enabled: boolean;
	expires_at?: string;
	created_at: string;
	updated_at: string;
	rate_limit: ShareRateLimit;
	ui_config: ShareUIConfig;
	owner_name?: string;
	workflow_name?: string;
	workflow_description?: string;
}

export interface ShareStats {
	views: number;
	runs: number;
	copies: number;
}

export interface ShareLog {
	id: number;
	share_id: number;
	visitor_ip: string;
	visitor_user_agent: string;
	action: "view" | "run" | "copy";
	accessed_at: string;
}

export interface ShareRateLimitStatus {
	enabled: boolean;
	limits: {
		daily_run_limit: number;
		hourly_run_limit: number;
		per_ip_daily_limit: number;
		cooldown_seconds: number;
	};
	usage: {
		ip_daily_count: number;
		daily_count: number;
		hourly_count: number;
		last_run_at?: string;
	};
	remaining: {
		daily: number;
		hourly: number;
		per_ip_daily: number;
		cooldown_remaining: number;
	};
}

export interface SharedWorkflowInfo {
	uuid: string;
	name: string;
	description: string;
	data: any;
	type: string;
	owner: string;
	share_info: {
		title: string;
		description: string;
		bill_to: "sharer" | "runner";
		ui_config: ShareUIConfig;
		rate_limit: ShareRateLimit;
		rate_limit_status?: ShareRateLimitStatus;
	};
}

export interface PublicShareInfo {
	share_uuid: string;
	title: string;
	description: string;
	bill_to: "sharer" | "runner";
	owner_name: string;
	workflow_name: string;
	workflow_description: string;
	created_at: string;
	expires_at?: string;
	rate_limit: ShareRateLimit;
	ui_config: ShareUIConfig;
}

export interface CreateShareRequest {
	title: string;
	description: string;
	bill_to: "sharer" | "runner";
	expires_at?: number;
	rate_limit?: Partial<ShareRateLimit>;
	ui_config?: Partial<ShareUIConfig>;
}

export interface UpdateShareRequest {
	title?: string;
	description?: string;
	bill_to?: "sharer" | "runner";
	is_enabled?: boolean;
	expires_at?: number;
	rate_limit?: Partial<ShareRateLimit>;
	ui_config?: Partial<ShareUIConfig>;
}

export interface ShareExecutionRequest {
	input: Record<string, any>;
}

export interface ShareCopyRequest {
	name?: string;
	description?: string;
}

export interface ShareLogsResponse {
	logs: ShareLog[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		has_next: boolean;
	};
}

export interface ShareCopyResponse {
	message: string;
	workflow: {
		uuid: string;
		name: string;
		description: string;
		created_at: string;
	};
}

// Share Management API (requires authentication)
export class SharesAPI {
	// Create a new share for a workflow
	static async createShare(workflowUuid: string, shareData: CreateShareRequest): Promise<WorkflowShare> {
		const res = await axios.post(`/workflows/${workflowUuid}/shares`, shareData);
		return res.data;
	}

	// Get all shares for a workflow
	static async getWorkflowShares(workflowUuid: string): Promise<{ shares: WorkflowShare[] }> {
		const res = await axios.get(`/workflows/${workflowUuid}/shares`);
		return res.data;
	}

	// Get specific share details with stats
	static async getShareDetails(workflowUuid: string, shareUuid: string): Promise<{ share: WorkflowShare; stats: ShareStats }> {
		const res = await axios.get(`/workflows/${workflowUuid}/shares/${shareUuid}`);
		return res.data;
	}

	// Update a share
	static async updateShare(workflowUuid: string, shareUuid: string, updateData: UpdateShareRequest): Promise<WorkflowShare> {
		const res = await axios.put(`/workflows/${workflowUuid}/shares/${shareUuid}`, updateData);
		return res.data;
	}

	// Delete a share
	static async deleteShare(workflowUuid: string, shareUuid: string): Promise<{ message: string }> {
		const res = await axios.delete(`/workflows/${workflowUuid}/shares/${shareUuid}`);
		return res.data;
	}

	// Get share access logs
	static async getShareLogs(workflowUuid: string, shareUuid: string, page: number = 1, limit: number = 20): Promise<ShareLogsResponse> {
		const res = await axios.get(`/workflows/${workflowUuid}/shares/${shareUuid}/logs?page=${page}&limit=${limit}`);
		return res.data;
	}
}

// Public Access API (no authentication required for most operations)
export class PublicSharesAPI {
	// Get basic share information
	static async getPublicShareInfo(shareUuid: string): Promise<PublicShareInfo> {
		const res = await axios.get(`/shared/${shareUuid}`);
		return res.data;
	}

	// Get workflow details for sharing page
	static async getSharedWorkflowInfo(shareUuid: string): Promise<SharedWorkflowInfo> {
		const res = await axios.get(`/shared/${shareUuid}/workflow`);
		return res.data;
	}

	// Execute a shared workflow
	static async executeSharedWorkflow(shareUuid: string, executionData: ShareExecutionRequest): Promise<Response> {
		// Note: This returns a Response object for streaming, not processed by axios interceptors
		const response = await fetch(`/v1/shared/${shareUuid}/executor`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(executionData),
		});
		
		if (!response.ok) {
			const errorData = await response.json();
			throw new Error(errorData.msg || 'Execution failed');
		}
		
		return response;
	}

	// Copy a shared workflow (requires authentication)
	static async copySharedWorkflow(shareUuid: string, copyData: ShareCopyRequest = {}): Promise<ShareCopyResponse> {
		const res = await axios.post(`/shared/${shareUuid}/copy`, copyData);
		return res.data;
	}
}

// Default configurations
export const DEFAULT_RATE_LIMIT: ShareRateLimit = {
	enabled: false,
	daily_run_limit: 100,
	hourly_run_limit: 10,
	per_ip_daily_limit: 20,
	cooldown_seconds: 5,
};

export const DEFAULT_UI_CONFIG: ShareUIConfig = {
	allow_view: true,
	allow_run: true,
	allow_copy: false,
	show_workflow_graph: true,
	welcome_message: "",
	custom_theme: {},
};

// Helper functions
export function isShareExpired(share: WorkflowShare | PublicShareInfo): boolean {
	if (!share.expires_at) return false;
	return new Date(share.expires_at) < new Date();
}

export function getShareUrl(shareUuid: string): string {
	const baseUrl = window.location.origin;
	return `${baseUrl}/shared/${shareUuid}`;
}

export function formatShareDate(dateString: string): string {
	return new Date(dateString).toLocaleString();
}

export function getRateLimitStatusText(status: ShareRateLimitStatus, t: (key: string) => string): string {
	if (!status.enabled) return t('shares.rate_limit_disabled');
	
	const remainingDaily = status.remaining.daily;
	const remainingHourly = status.remaining.hourly;
	const remainingPerIP = status.remaining.per_ip_daily;
	const cooldownRemaining = status.remaining.cooldown_remaining;
	
	if (cooldownRemaining > 0) {
		return t('shares.cooldown_remaining', { seconds: cooldownRemaining });
	}
	
	if (remainingDaily <= 0) {
		return t('shares.daily_limit_exceeded');
	}
	
	if (remainingHourly <= 0) {
		return t('shares.hourly_limit_exceeded');
	}
	
	if (remainingPerIP <= 0) {
		return t('shares.ip_limit_exceeded');
	}
	
	return t('shares.rate_limit_ok', { 
		daily: remainingDaily, 
		hourly: remainingHourly, 
		ip: remainingPerIP 
	});
}